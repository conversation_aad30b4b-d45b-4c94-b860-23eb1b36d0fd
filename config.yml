prefix: '&c&l⚡ VULCAN &8&l| &7'
alerts:
  # This is the chat format that will be followed when a player fails a check.
  # Valid placeholders are %player% (player name), %max-vl% (max violations), %check% (name of check), %description% (check description),
  # %version% (player's client version), %dev% (* for whether or not the check is experimental), %vl% (violation level),
  # %ping% (the player's ping), %tps% (the server's TPS), and %type% (character denoting the type for the check, A, B, C, etc),
  # %severity% for changing colors depending on the violations (options found in severity option below), %x% (player's X-coordinate),
  # %y% (player's Y-coordinate), %z% (player's Z-coordinate), %world% (player's world), %complex-type% which will show a more
  # advanced name for the check, such as Speed (Ground) instead of Speed (Type B)
  format: '%prefix% &c%player% &7bukott &f%check% %dev%&7(&fTípus %type%&7)%dev% &8[&c%vl%&8/&c%max-vl%&8]
    &c⚠'
  # This is the hover text that will be displayed when you hover over the alert message in chat.
  # All of the same placeholders as the ones for the format are valid.
  hover-message:
  - '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
  - '&c&l⚡ JÁTÉKOS INFORMÁCIÓK ⚡'
  - ''
  - '&7Ping: &c%ping%ms &8| &7TPS: &c%tps% &8| &7Verzió: &c%version%'
  - '&7Kliens: &c%client-brand%'
  - ''
  - '&7Leírás:'
  - '&f%description%'
  - ''
  - '&7További információk:'
  - '&f%info%'
  - ''
  - '&a&l➤ &7Kattints a teleportáláshoz &b%player% &7játékoshoz!'
  - '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
  # This is the command that will be executed when you click the alert message. Unfortunately we are limited to one
  # command per ChatComponent so we use a little magic and make our own command that is able to execute a list of any commands.
  # Valid placeholders
  click-commands:
  - /vulcan clickalert %player%
  # The commands that will be run when you use /vulcan clickalert %player% (must be a player parameter)
  click-alert-command-commands:
  - tp %player%
  # The message that will be sent when you toggle on alerts. Valid placeholder are %prefix%.
  toggled-on-message: '%prefix% &aVulcan riasztások &2&l✓ BEKAPCSOLVA&a!'
  # The message that will be sent when you toggle off alerts. Valid placeholder are %prefix%.
  toggled-off-message: '%prefix% &cVulcan riasztások &4&l✗ KIKAPCSOLVA&c!'
  # Whether or not alerts will be printed to the console.
  print-to-console: true
  # The format for printing alerts to the console
  console-format: '[⚡ VULCAN] %player% bukott %check% %dev%(Típus %type%)%dev% (%vl%/%max-vl%)'
  # This is a fully customizable list of commands that will be executed each time a player flags a check, just in case
  # you want to do anything special when they flag without needing to use the API. All valid placeholders are the same
  # as listed above once again.
  custom-commands: []
  # You can use this section to color code your alerts or add chances to them depending on the violation level.
  severity:
    violations:
      '2': 4 # Values less than this number will be the color '1' below. Values greater than or equal to this number and less than '3' will be the color 2
      '3': 8 # Values greater than or equal to this number and higher than '2' will be the color '3' below
      '4': 12 # Values greater than or equal to this number and higher than '3' will be the color '4' below
      '5': 16 # Values greater than or equal to this number and higher than '4' will be the color '5' below
      '6': 16 # Values greater than or equal to this number and higher than '5' will be the color '6' below
      '7': 16 # Values greater than or equal to this number and higher than '6' will be the color '7' below
      '8': 16 # Values greater than or equal to this number and higher than '7' will be the color '8' below
      '9': 16 # Values greater than or equal to this number and higher than '8' will be the color '9' below
      '10': 16 # Values greater than or equal to this number and higher than '9' will be the color '10' below
    colors: # This also supports hex color codes (in 1.16!+)
      '1': '&a' # The color that this severity should be, whatever is less than '2' in the section above
      '2': '&e' # The color that this severity should be, whatever is less than '3' and greater than or equal to '2' in the section above
      '3': '&6' # The color that this severity should be, whatever is less than '4' and greater than or equal to '3' in the section above
      '4': '&c' # The color that this severity should be, whatever is less than '5' and greater than or equal to '4' in the section above
      '5': '&4' # The color that this severity should be, whatever is less than '6' and greater than or equal to '5' in the section above
      '6': '&4' # The color that this severity should be, whatever is less than '7' and greater than or equal to '6' in the section above
      '7': '&4' # The color that this severity should be, whatever is less than '8' and greater than or equal to '7' in the section above
      '8': '&4' # The color that this severity should be, whatever is less than '9' and greater than or equal to '8' in the section above
      '9': '&4' # The color that this severity should be, whatever is less than '10' and greater than or equal to '9' in the section above
      '10': '&4' # The color that this severity should be, whatever is less than '11' and greater than or equal to '10' in the section above
  # The symbol that will be displayed for the %dev% placeholder to denote experimental checks.
  experimental-symbol: '*'
log-file:
  # Whether or not the log file is enabled
  enabled: true
  # The message that is logged when a player fails a check.
  # Valid placeholders are %player% (player name), %max-vl% (max violations), %check% (name of check), %description% (check description),
  # %version% (player's client version), %dev% (* for whether or not the check is experimental), %vl% (violation level),
  # %ping% (the player's ping), %tps% (the server's TPS), and %type% (character denoting the type for the check, A, B, C, etc).
  alert-message: '%player% bukott %check% %dev%(Típus %type%)%dev% [VL: %vl%] [Ping:
    %ping%] [TPS: %tps%] [Verzió: %version%] [Kliens: %client-brand%]'
  # The message that is logged when a player is punished for a check. Valid placeholders are the same as listed above.
  # Further details about the punishment will also be logged into the punishments.txt file if enabled (see next section).
  punishment-message: '%player% meg lett büntetve %check% %dev%(Típus %type%)%dev% miatt [VL:
    %vl%] [Ping: %ping%] [TPS: %tps%] [Kliens: %client-brand%]'
unloaded-chunks:
  # Whether or not people should be setback for sending motion which is equal to unloaded chunks
  setback-enabled: false
  # Whether or not staff should be alerted, set to '' to disable this message
  message: '%prefix% &c%player% &7vissza lett állítva betöltetlen chunk miatt! &c⚠'
  # Max amount of ticks before someone gets set back
  max-ticks: 40
punishment-file:
  # Whether or not the punishments.txt file is enabled.
  enabled: true
violation-reset:
  # Whether or not violations should reset automatically
  enabled: true
  # The interval (in minutes) in which violations should automatically be reset.
  interval-in-minutes: 8
  # Whether or not a message will be sent when violations are reset (this is only sent to staff who have /alerts enabled).
  message-enabled: true
  # The message that is sent (only to staff with /alerts enabled) when violations are reset.
  message: '%prefix% &aAz összes online játékos szabálysértése törölve! &2✓'
client-brand-alerts:
  # Whether or not Vulcan should even try to resolve the player's client brand - disabling this won't register
  # the messaging channel at all.
  resolve: true
  # Whether or not client brand alerts are enabled.
  enabled: true
  # A list of client brands for which alerts will not be sent for. This doesn't have to match exactly, i.e
  # 'lunar' will be able to pick up 'lunarclient', etc.
  ignore-alerts-list:
  - vanilla
  # The message sent when a player joins the server. Valid placeholder are %player (player name), %prefix% (plugin prefix),
  # %client-brand% (the user's client brand), and %client-version% (the user's client version).
  message: '%prefix% &c%player% &7csatlakozott &f%client-brand% &7klienssel! &e⚡'
  # A list of client brands that will not be allowed and will be kicked automatically when they join. This uses
  # string comparison so it doesnt need to be exact, for example, 'forge' will block Forge, 'lunar' will block lunar, etc.
  blocked-client-brands: []
  unallowed-client-brand-kick-message: '&c&l⚠ TILTOTT KLIENS ⚠\n\n&7Nem csatlakozhatsz
    &c%client-brand% &7klienssel!\n&7Használj engedélyezett klienst!'
  # Whether or not client brand alerts should be sent to the console
  console: false
  # The message that should be printed to the console (if the above option is enabled)
  console-message: '[⚡ VULCAN] %player% csatlakozott %client-brand% klienssel!'
  # Whether or not the client brand whitelist should be enabled. Anyone joining with a client brand that is not
  # included in the list below will not be allowed to join
  whitelist-enabled: false
  # The client brands that will be whitelisted This uses string comparison so it doesnt need to be exact, for example,
  # 'forge' will block Forge, 'lunar' will block lunar, etc.
  whitelisted-client-brands: []
connection:
  invalid:
    kick-message: 'Internal Exception: io.netty.handler.timeout.ReadTimeoutException'
    staff-alert-message: '%prefix% &c%player% &7ki lett rúgva érvénytelen megerősítő
      csomag miatt! (ID: &c%id%&7) &c⚠'
    console-alert-message: '[⚡ VULCAN] &c%player% &7ki lett rúgva érvénytelen megerősítő
      csomag miatt! (ID: &c%id%&7)'
  out-of-order:
    kick-message: 'Internal Exception: io.netty.handler.timeout.ReadTimeoutException'
    staff-alert-message: '%prefix% &c%player% &7was kicked for sending an out of order
      confirmation packet! (&c%id% &7!= &c%last-id%&7)'
    console-alert-message: '[Vulcan] %player% was kicked for sending an out of order
      confirmation packet! (%id% != %last-id%)'
  no-response:
    delay-in-seconds: 30
    kick-message: 'Internal Exception: io.netty.handler.timeout.ReadTimeoutException'
    staff-alert-message: '%prefix% &c%player% &7was kicked for not replying to confirmation
      packets! (&c%delay%&7ms)'
    console-alert-message: '[Vulcan] %player% was kicked for not replying to confirmation
      packets! '
  keepalive:
    kick-enabled: true
    max-delay: 45000
    kick-message: 'Internal Exception: io.netty.handler.timeout.ReadTimeoutException'
    staff-alert-message: '%prefix% &c%player% &7was kicked for not replying to KeepAlive
      Packets! &7(&c%delay%ms&7)'
    console-message: '[Vulcan] &c%player% &7was kicked for not replying to KeepAlive
      Packets! &7(&c%delay%ms&7)'
  max-ping:
    kick-enabled: true
    max-ping: 20000
    max-ticks: 20
    kick-message: 'Internal Exception: io.netty.handler.timeout.ReadTimeoutException'
    staff-alert-message: '%prefix% &c%player% &7was kicked for having too high ping!
      &7(&c%ping%ms&7)'
    console-message: '[Vulcan] &c%player% &7kicked for having too high ping! &7(&c%ping%ms&7)'
punishments:
  # The message that is sent when a player is punished (only sent to staff with /alerts enabled)
  message: '%prefix% &c%player% &7meg lett büntetve &f%check% &7miatt (&fTípus %type%&7)
    &8[&4x%vl%&8] &c⚡'
  # The message that will be broadcast to the whole server when a player is punished
  broadcast:
  - '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
  - '&c&l⚡ VULCAN &7észlelte, hogy &c&l%player% &7csal, ezért'
  - '&7eltávolította a hálózatból. &8| &aBiztonságban vagy! &a✓'
  - '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
judgement-days:
  # The message that wil be broadcast to the server when someone has executed /jday start
  started-broadcast:
  - '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
  - '&4&l⚡ VULCAN ÍTÉLET NAPJA v1 &4&lELKEZDŐDÖTT... &c⚠'
  - '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
  # The message that will be broadcast to the server when the judgement day has ended.
  ended-broadcast:
  - '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
  - '&a&l✓ Az Ítélet Napja véget ért... &c%amount% &ajátékos lett büntetve.'
  - '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
  # The commands that will be executed, %player% indicates a player who was added to judgement day (/jday add (name)))
  commands:
  - kick %player% &c&l⚡ [VULCAN] Ítélet Napja v1 ⚡\n\n&7Csalás gyanúja miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
  # The broadcast that will be sent to the server when someone is punished in a judgement day.
  broadcast:
  - '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
  - '&c&l%player% &7ki lett rúgva &c&l[VULCAN] Ítélet Napja v1 &7miatt.'
  - '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
  # How long we should wait in between players who are queued in judgement days
  cooldown: 1000
  # Whether or not judgement days should run automatically at a specified interval (if the above is true)
  run-at-interval: false
  # If the above is true, the interval at which they should run (in ticks, 20 ticks = 1 second. default = 36000 ticks = 30mins)
  interval: 36000
freeze:
  # A customizable list of commands that will be executed when a player logs out while frozen
  logged-out-while-frozen-commands: []
ghost-blocks-fix:
  # Whether or not ghost block fix should be enabled at all.
  enabled: true
  # Whether or not Vulcan should try to account for ghost water blocks
  ghost-water-fix: true
  # Below this TPS this option won't do anything. Set to -1 to disable
  minimum-tps: 18.5
  # Whether players on older than 1.17 client versions will be ignored from ghost blocks at y1 due to ViaVersion settings
  ignore-legacy-via: true
  # Buffer settings
  buffer:
    max: 3
    decay: 0.1
  # Whether or not setbacks for ghost blocks should be enabled.
  setback: true
  # Whether or not block change packets should be sent around the bottom of the player reupdating the blocks below them
  update-blocks: false
  # How many ticks should they be in the air before being flagged as on a ghost block (default 3)
  ticks: 3
  # Whether or not the player will get sent a message when ghost blocks around them are updated
  message-enabled: false
  # The message that will get sent to players when they're on a ghost block (if the above is enabled)
  message: '&c&l⚠ &cSzellem blokkon jártál! Frissítve lett! &c⚡'
  # Whether or not staff should be sent a message when someone is setback for being on a ghost block
  staff-message-enabled: true
  # The message that will be sent to staff when a player's position is updated for being on a ghost block.
  staff-message: '%prefix% &c%player% &7pozíciója frissítve lett szellem blokk miatt!
    (%world%, %x%, %y%, %z%) &8[%ticks%] &c⚠'
  # Whether or not the ghost block message should be printed to console
  print-to-console: true
  # The message that is printed to console if the above is true.
  console-message: '[⚡ VULCAN] %player% pozíciója frissítve lett szellem blokk miatt!
    (%world%, %x%, %y%, %z%) [%ticks%]'
messages:
  no-permission: '&c&l⚠ &cNincs jogosultságod ehhez a parancshoz!'
  cant-execute-from-console: '&c&l⚠ &cEzt a parancsot nem futtathatod konzolból!'
  ban-command-syntax: '%prefix% &7Helyes szintaxis: &c/vulcan ban (játékos)&7.'
  kb-command-syntax: '%prefix% &7Helyes szintaxis: &c/vulcan knockback (játékos) &8|
    &c/vulcan kb (játékos)&7.'
  profile-command-syntax: '%prefix% &7Helyes szintaxis: &c/vulcan profile (játékos)&7.'
  jday-command-syntax: '%prefix% &7Helyes szintaxis: &c/jday add (játékos) &8| &c/jday
    execute &8| &c/jday list &8| &c/jday remove (játékos)&7.'
  reload-success: '%prefix% &a&l✓ Vulcan sikeresen újratöltve!'
  invalid-target: '%prefix% &c&l⚠ A megadott játékos nem található!'
  kb-test-success: '%prefix% &a&l✓ Knockback teszt sikeresen végrehajtva %player%
    játékoson!'
  jday-no-pending-bans: '%prefix% &c&l⚠ Nincsenek függőben lévő banok!'
  jday-added-to-list: '%prefix% &c%player% &7sikeresen hozzáadva a listához! &a✓'
  reset-command: '%prefix% &aAz összes online játékos szabálysértése törölve! &2✓'
  disable-check-command-syntax: '%prefix% &cHelyes szintaxis: /vulcan disablecheck
    (ellenőrzés neve) - kis/nagybetű érzékeny.'
  invalid-check: '%prefix% &c&l⚠ Érvénytelen ellenőrzés név! Példák: AimA, BadPacketsF,
    AutoClickerJ.'
  removed-check: '%prefix% &c&l✓ %check% ellenőrzés letiltva!'
  violations-command-syntax: '%prefix% &cHelyes szintaxis: /vulcan violations (játékos)'
  no-logs: '%prefix% &c&l⚠ Ez a játékos nem váltott ki naplókat!'
  cps-command-syntax: '%prefix% &cHelyes szintaxis: /vulcan cps (játékos)'
  connection-command-syntax: '%prefix% &cHelyes szintaxis: /vulcan connection (játékos)'
  logs-command-syntax: '%prefix% &cHelyes szintaxis: /logs (játékos) (oldal)'
  no-logs-file: '%prefix% &c&l⚠ Nincs naplófájl!'
  logs-command-no-logs: '%prefix% &c&l⚠ Nem találhatók naplók %player% játékoshoz!'
  no-page: '%prefix% &c&l⚠ Nincs %page% oldal %player% játékoshoz!'
  update-available: '%prefix% &eÚj frissítés érhető el a &cVulcan&e-hoz! (&cv%new-version%&e)
    &a⚡'
  latest-version: '%prefix% &a&l✓ A Vulcan legújabb verzióját futtatod!'
  injection-failure: '&c&l⚠ Túl gyorsan csatlakoztál! Próbáld újra!'
  frozen: '&c&l❄ BEFAGYASZTVA! &7Várd a staff utasításait!'
  logged-out-while-frozen: '%prefix% &c%player% &7kilépett befagyasztva! &c⚠'
  froze: '%prefix% &aBefagyasztottad &a&l%player%&a játékost! &2❄'
  unfroze: '%prefix% &cKiolvasztottad &c&l%player%&c játékost! &4🔥'
  freeze-command-syntax: '%prefix% &cHelyes szintaxis: /vulcan freeze (játékos)'
  shuffle-command-syntax: '%prefix% &cHelyes szintaxis: /vulcan shuffle (játékos)'
  shuffled-hotbar: '%prefix% &a&l✓ Hotbar sikeresen megkeverve %player% játékosnál!'
  rotate-command-syntax: '%prefix% &cHelyes szintaxis: /vulcan rotate (játékos)'
  randomly-rotated: '%prefix% &a&l✓ %player% sikeresen elforgatva!'
  reset-command-syntax: '%prefix% &cHelyes szintaxis: /vulcan reset (játékos)'
  violations-reset: '%prefix% &a&l✓ %player% szabálysértései sikeresen törölve!'
  unknown-command: '%prefix% &c&l⚠ Ismeretlen parancs!'
  unfroze-staff-broadcast: '%prefix% &c%player% &7ki lett olvasztva &c%staff% &7által!
    &4🔥'
  froze-staff-broadcast: '%prefix% &c%player% &7be lett fagyasztva &c%staff% &7által!
    &2❄'
  removed-from-jday: '%prefix% &a&l✓ %player% sikeresen eltávolítva az Ítélet Napjából!'
  jday-remove-syntax: '%prefix% &cHelyes szintaxis: /jday remove (játékos)'
  punishlogs-syntax: '%prefix% &cHelyes szintaxis: /punishlogs (játékos)'
  sent-test-alert: '%prefix% &a&l✓ Teszt riasztás sikeresen végrehajtva!'
  description-command-syntax: '%prefix% &cHelyes szintaxis: /vulcan description (ellenőrzésnév) '
commands:
  help:
  - '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
  - '&c&l⚡ VULCAN ANTI-CHEAT v%version% &c&l⚡'
  - '&7Fejlesztő: &cfrep (.frap) &8| &7Magyar fordítás'
  - ''
  - '&c&l➤ ALAPVETŐ PARANCSOK:'
  - '&c/vulcan help &8- &7Megnyitja ezt a súgó menüt'
  - '&c/vulcan profile (játékos) &8- &7Játékos Vulcan profiljának megjelenítése'
  - '&c/vulcan kick (játékos) &8- &7Játékos kirúgása Vulcan-on keresztül'
  - '&c/vulcan reload &8- &7Összes fájl újratöltése'
  - ''
  - '&c&l➤ ELLENŐRZÉS PARANCSOK:'
  - '&c/vulcan disablecheck (ellenőrzés) &8- &7Ellenőrzés letiltása'
  - '&c/vulcan violations (játékos) &8- &7Játékos szabálysértéseinek megjelenítése'
  - '&c/vulcan checks &8- &7Összes engedélyezett ellenőrzés megjelenítése'
  - ''
  - '&c&l➤ JÁTÉKOS PARANCSOK:'
  - '&c/vulcan cps (játékos) &8- &7Játékos CPS-ének megjelenítése'
  - '&c/vulcan connection (játékos) &8- &7Kapcsolat információk megjelenítése'
  - '&c/vulcan freeze (játékos) &8- &7Játékos befagyasztása/kiolvasztása'
  - '&c/vulcan rotate (játékos) &8- &7Játékos véletlenszerű elforgatása'
  - '&c/vulcan shuffle (játékos) &8- &7Hotbar véletlenszerű megkeverése'
  - '&c/vulcan kb (játékos) &8- &7Knockback alkalmazása játékosra'
  - ''
  - '&c&l➤ STATISZTIKA & NAPLÓK:'
  - '&c/vulcan top &8- &7Legtöbb szabálysértéssel rendelkező játékosok'
  - '&c/logs (játékos) (oldal) &8- &7Játékos szabálysértéseinek olvasása'
  - '&c/punishlogs (játékos) &8- &7Játékos büntetéseinek ellenőrzése'
  - ''
  - '&c&l➤ ÍTÉLET NAPJA:'
  - '&c/jday add (játékos) &8- &7Játékos hozzáadása az Ítélet Napjához'
  - '&c/jday execute &8- &7Ítélet Napjának végrehajtása'
  - ''
  - '&c&l➤ EGYÉB:'
  - '&c/alerts &8- &7Csalás riasztások be/kikapcsolása'
  - '&c/vulcan testalert &8- &7Discord webhook/integráció tesztelése'
  - '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
  ban:
    commands:
    - ban %player% &c&l⚡ [VULCAN] Tisztességtelen Előny ⚡\n\n&7Csalás miatt ki lettél tiltva!\n&7Fellebbezés: discord.gg/server
    broadcast:
    - '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
    - '&c&l⚡ VULCAN &7észlelte, hogy &c&l%player% &7csal, ezért'
    - '&7véglegesen eltávolította a hálózatból. &8| &aBiztonságban vagy! &a✓'
    - '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
  profile:
  - '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
  - '&c&l⚡ VULCAN PROFIL - %player% ⚡'
  - ' &8➤ &7UUID: &f%uuid%'
  - ''
  - '&c&l📊 KLIENS INFORMÁCIÓK:'
  - '&7Verzió: &c%client-version%'
  - '&7Kliens: &c%client-brand%'
  - '&7Érzékenység: &c%sensitivity%%'
  - '&7Utolsó CPS: &c%cps% CPS'
  - '&7Mozgási sebesség: &c%move-speed%'
  - ''
  - '&c&l⚠ SZABÁLYSÉRTÉSEK (&c%total-violations%&c&l):'
  - ' &8➤ &7Harc: &c%combat-violations%'
  - ' &8➤ &7Mozgás: &c%movement-violations%'
  - ' &8➤ &7Játékos: &c%player-violations%'
  - ''
  - '&c&l🔍 ÜTKÖZÉSEK:'
  - '&8➤ &7Közeli blokkok: &f%nearby-blocks%'
  - '&8➤ &7Közeli entitások: &f%nearby-entities%'
  - ''
  - '&c&l🧪 BÁJITAL HATÁSOK: &f%potion-effects%'
  - '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
  cps: '%prefix% &c%player% &7utolsó CPS-e: &c&l%cps% &c⚡'
  connection:
  - '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
  - '&c&l🌐 KAPCSOLAT INFORMÁCIÓK - %player%'
  - ''
  - '&7Tranzakció Ping: &c%transaction-ping%ms'
  - '&7KeepAlive Ping: &c%keepalive-ping%ms'
  - ''
  - '&7Utolsó válasz: &c%last-replied-transaction%ms &7ezelőtt'
  - '&7Várakozó tranzakciók: &c%queued-transactions%'
  - ''
  - '&7Repülési késleltetés: &c%flying-delay%ms'
  - '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
  logs:
    color: '&7'
    header-footer: '&8&l▬▬▬▬▬▬▬▬ &c&l📋 NAPLÓK - %player% &8(&c%page%&8/&c%max-pages%&8) &8&l▬▬▬▬▬▬▬▬'
  top:
  - '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
  - '&c&l🏆 LEGTÖBB ÖSSZES SZABÁLYSÉRTÉS:'
  - ' &8➤ &7%total-1-name% &8(&c%total-1-violations% VL&8)'
  - ' &8➤ &7%total-2-name% &8(&c%total-2-violations% VL&8)'
  - ' &8➤ &7%total-3-name% &8(&c%total-3-violations% VL&8)'
  - ' &8➤ &7%total-4-name% &8(&c%total-4-violations% VL&8)'
  - ' &8➤ &7%total-5-name% &8(&c%total-5-violations% VL&8)'
  - ''
  - '&c&l⚔ LEGTÖBB HARCI SZABÁLYSÉRTÉS:'
  - ' &8➤ &7%combat-1-name% &8(&c%combat-1-violations% VL&8)'
  - ' &8➤ &7%combat-2-name% &8(&c%combat-2-violations% VL&8)'
  - ' &8➤ &7%combat-3-name% &8(&c%combat-3-violations% VL&8)'
  - ' &8➤ &7%combat-4-name% &8(&c%combat-4-violations% VL&8)'
  - ' &8➤ &7%combat-5-name% &8(&c%combat-5-violations% VL&8)'
  - ''
  - '&c&l🏃 LEGTÖBB MOZGÁSI SZABÁLYSÉRTÉS:'
  - ' &8➤ &7%movement-1-name% &8(&c%movement-1-violations% VL&8)'
  - ' &8➤ &7%movement-2-name% &8(&c%movement-2-violations% VL&8)'
  - ' &8➤ &7%movement-3-name% &8(&c%movement-3-violations% VL&8)'
  - ' &8➤ &7%movement-4-name% &8(&c%movement-4-violations% VL&8)'
  - ' &8➤ &7%movement-5-name% &8(&c%movement-5-violations% VL&8)'
  - ''
  - '&c&l👤 LEGTÖBB JÁTÉKOS SZABÁLYSÉRTÉS:'
  - ' &8➤ &7%player-1-name% &8(&c%player-1-violations% VL&8)'
  - ' &8➤ &7%player-2-name% &8(&c%player-2-violations% VL&8)'
  - ' &8➤ &7%player-3-name% &8(&c%player-3-violations% VL&8)'
  - ' &8➤ &7%player-4-name% &8(&c%player-4-violations% VL&8)'
  - ' &8➤ &7%player-5-name% &8(&c%player-5-violations% VL&8)'
  - '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
  jday:
    list-header-footer: '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
    list-format: ' &8➤ &c%name%'
  punishlogs:
    header-footer: '&8&l▬▬▬▬▬▬ &c&l⚖ BÜNTETÉSI NAPLÓK - %player% &8&l▬▬▬▬▬▬'
  description: '%prefix% &c%check% &7leírása: &f%description%'
punishment-statistics-broadcast:
  # Whether or not Vulcan should broadcast how many players it has punished.
  enabled: false
  # The interval at which the message should be broadcasted in (in ticks) (default = 18000 = 15min)
  interval: 18000
  # The message that should be broadcasted
  broadcast:
  - '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
  - '&f      &c&l⚡ VULCAN STATISZTIKA &c&l⚡'
  - ''
  - '&7Az elmúlt &f7 napban &7a &c&lVulcan &7összesen &c&l%amount% &7játékost'
  - '&7büntetett meg csalás miatt. &8| &7Biztonságban vagy! &a✓'
  - ''
  - '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
settings:
  # Whether or not alerts will automatically be enabled on-join for players who have the permission "vulcan.alerts"
  toggle-alerts-on-join: true
  # Whether or not the severity placeholder should be based off of per-check violations or the total violations of the player
  per-check-severities: true
  # Whether or not cinematic mode will be checked for certain aim checks. This isn't really a big deal and can be
  # left disabled as this will pretty much NEVER happen unless done intentionally. Enabling this will slow
  # detection speeds for certain checks.
  cinematic: false
  # This is just here in case I forget to remove a debug message when I post an update. If I forget to remove a debug
  # message and you see [Vulcan Debug] on your server, disable this (it won't effect you if this is enabled or disabled
  # unless I forget to remove a debug message, but if you see [Vulcan Debug] in chat, then set this to false and reload).
  debug: true
  # Whether or not 'A new update is available!' message will be set on join for players with 'vulcan.admin' permission
  check-for-updates: true
  # A setting for PacketEvents, we may ask you to change this if you're having issues injecting. If everything is working
  # fine, don't touch this.
  inject-early: true
  # This is the time (in ticks - 20 ticks per second - default value is 80 (4 seconds)) of how long players should be exempt from certain
  # checks after velocity gets applied to them via another plugin or something (such as Launchpads, crate plugins, etc etc).
  max-velocity-ticks: 50
  # This is the max file size (in KB) before the violations.txt should be zipped and put into the logs folder
  max-logs-file-size: 7500
  # If you have a bungee network and want to have a specific server name for punishment/alert messages, etc
  server-name: Default
  # Whether or not alert messages should be sent on the main thread or asynchronously. If your server is on bungee, it is recommended to keep this
  # disabled as sending large chat component messages with so much info (hover, click commands, etc) like the alert commands can
  # sometimes cause people to be kicked for 'The server you were on has went down' or something.
  # TLDR:
  # - Bungeecord Networks: leave this FALSE
  # - Single servers: set this to TRUE to save some performance
  async-alerts: false
  enable-api: true
  # Whether or not the update checker should be enabled
  update-checker: true
  # The delay in milliseconds (1000ms = 1 second)
  punishment-delay: 10000
  # Whether or not Vulcan should automatically ignore Bedrock players using Floodgate's API.
  ignore-floodgate: true
  # This is highly highly recommended to keep false as it can be spoofed by the client, however, if you're using Geyser and
  # for whatever reason can't get Floodgate to work, then enable this. If you have floodgate-bukkit on your server
  # and Geyser is working fine, then don't touch this.
  ignore-flags-geyser-client-brand: false
  # How long we should wait for everything to initialize before players start being checked (in milliseconds). Default
  # value is 2500ms.
  join-check-wait-time: 2500
  # Whether or not players with UUID's starting with '000000' should be ignored and not flagged - used for exemptions of
  # Geyser players if all else fails.
  ignore-geyser-uuids: true
  # Whether or not we should ignore flags for players whose usernames start with a certain character/string of characters
  ignore-geyser-prefixes: true
  # The string to be ignored - all players names who start with a * will not be flagged (case sensitive if not symbols)
  ignore-geyser-prefix: '*'
  # The max violation (violations above this won't be sent)
  max-alert-violation: 250
  # How long should people be exempt from checks when they were in /fly (default = 40 ticks, 2 seconds)
  flight-cooldown: 40
  # What the /vulcan top command should display if there aren't enough users for it to sort
  top-command-unresolved: Unresolved
  # Whether or not players who are in the judgement day queue should be able to be kickned again.
  ignore-if-in-judgement-day: false
  # Whether or not players who join with Vivecraft should be ignored. Be careful with this as it can be spoofed.
  ignore-vivecraft: false
  # Scaffolding are really broken blocks, whether or not we should be more lenient for them for certain checks.
  lenient-scaffolding: true
  # Whether or not bStats should be loaded
  bstats: true
  # Whether or not Vulcan should account for entity collisions
  entity-collision: true
  # Whether or not Vulcan should hook into mcMMO if it is available.
  hook-mcmmo: true
  # Whether or not Vulcan should hook into MythicMobs if it is available.
  hook-mythicmobs: true
  # Whether or not Vulcan should hook into GSit if it is available
  hook-gsit: true
  # Whether or not Vulcan should hook with Brewery if available
  hook-brewery: true
  # Whether or not Vulcan should ignore alerts for violations which are greater than the max violations threshold
  ignore-alerts-over-max-violations: false
  # The amount greater than the max violations that should be ignored (example, 3 would mean that if the max violations
  # was set to 10, alerts greater than VL 13 would be ignored)
  ignore-alerts-over-max-violations-amount: 3
  # Whether or not Vulcan should warn you if you may be using an incompatible spigot fork.
  incompatability-manager: true
  # Whether or not Vulcan should send plugin messages
  plugin-messaging: true
  # Whether or not Vulcan should setback players when they are lower than their last onGround position (this can lead to
  # things such as anti-voids, so not particularly recommended if you have something like a SkyWars/BedWars server)
  dont-setback-lower-positions: false
  # Minimum ticks existed before a player is chcked, similar to join-check-wait-time above
  min-ticks-existed: 3
  # Whether or not we should calculate the distance between the player and WorldBorder to prevent false flags in the
  # Velocity checks. Only needed on very specific servers.
  velocity-world-border: false
  # Whether or not Vulcan should account for having 10 kabillion entities in one place that launches you very fast and could
  # 'false' some checks. Will not matter on 99% of servers
  entity-cram-fix-enabled: false
  # The amount of entities that should be accounted for
  entity-cram-entities-amount: 10
  # How long the checks should be exempted for if the above entities are encountered
  entity-cram-exempt-ticks: 30
  # Support for attribute modifiers
  handle-attributes: true
discord-webhook:
  # Whether or not cooldown is enabled
  cooldown: false
  # How often the counter should reset in ticks (default = 12000 ticks or 10 mins, 20 ticks = 1 second)
  cooldown-reset-ticks: 12000
  # Max webhooks allowed in the above time period
  cooldown-max-webhooks: 100
  # The section for the Discord Webhook alerts
  alerts:
    # Whether or not Discord Webhook Alerts should be enabled
    enabled: false
    # Paste the URL to your webhook here. For how to create a webhook, please see https://support.discord.com/hc/en-us/articles/*********-Intro-to-Webhooks
    url: insert-webhook-url-here
    # The avatar for the webhook
    avatar: https://i.imgur.com/JPG1Kwk.png
    # Thumbnail for the webhook
    thumbnail: http://cravatar.eu/avatar/%name%/64.png
    # Colors for the webhook (use a website like https://www.rapidtables.com/web/color/RGB_Color.html to pick colors)
    color-r: 252
    color-g: 140
    color-b: 3
    # Username of the webhook
    username: Vulcan
    # Title of the webhook
    title: Vulcan Alert
    # Description of the webhook
    description: '**%player%** failed %check% %dev%(Type %type%)%dev% [%vl%/%max-vl%]'
    # Content will send text outside of the embed, so you can tag roles or people for example.
    content: ''
    information-field: false
    client-version-field: true
    client-brand-field: true
    server-name-field: false
    description-field: false
    location-field: true
    ping-tps-field: true
  punishments:
    # Same settings and explanations as above
    enabled: false
    url: insert-webhook-url-here
    avatar: https://i.imgur.com/JPG1Kwk.png
    thumbnail: http://cravatar.eu/avatar/%name%/64.png
    color-r: 255
    color-g: 0
    color-b: 0
    username: Vulcan
    title: Vulcan Punishment
    description: '**%player%** was punished for %check% (Type %type%) [VL: %vl%]'
    content: ''
    description-field: false
    server-name-field: false
    client-brand-field: false
    client-version-field: false
    ping-tps-field: false
    location-field: false
checks:
  combat:
    aim:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 8
        alert-interval: 1
        dont-alert-until: 3
        maximum-ping: 250
        minimum-tps: 18.0
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] Aimbot észlelve ⚡\n\n&7Tisztességtelen célzás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 8
          multiple: 0.85
          decay: 0.15
        setback:
          enabled: true
          vl-to-setback: 2
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 4
          shuffle-every: 3
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 3
          rotate-every: 2
      b:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 12
        alert-interval: 1
        dont-alert-until: 4
        maximum-ping: 200
        minimum-tps: 18.5
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] Aimbot észlelve ⚡\n\n&7Tisztességtelen célzás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 12
          multiple: 0.75
          decay: 0.25
        setback:
          enabled: true
          vl-to-setback: 3
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 6
          shuffle-every: 4
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 5
          rotate-every: 3
      c:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 10
        alert-interval: 1
        dont-alert-until: 3
        maximum-ping: 180
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] Aimbot észlelve ⚡\n\n&7Tisztességtelen célzás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 10
          multiple: 0.8
          decay: 0.2
        setback:
          enabled: true
          vl-to-setback: 2
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 5
          shuffle-every: 3
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 4
          rotate-every: 2
      d:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 5
        maximum-ping: 220
        minimum-tps: 18.0
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] Aimbot észlelve ⚡\n\n&7Tisztességtelen célzás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 15
          multiple: 0.7
          decay: 0.3
        setback:
          enabled: true
          vl-to-setback: 4
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 8
          shuffle-every: 5
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 6
          rotate-every: 4
      e:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 6
        alert-interval: 1
        dont-alert-until: 2
        maximum-ping: 150
        minimum-tps: 19.5
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] Aimbot észlelve ⚡\n\n&7Tisztességtelen célzás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 8
          multiple: 0.9
          decay: 0.1
        setback:
          enabled: true
          vl-to-setback: 1
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 3
          shuffle-every: 2
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 2
          rotate-every: 1
      f:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 18
        alert-interval: 1
        dont-alert-until: 6
        maximum-ping: 250
        minimum-tps: 17.5
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] Aimbot észlelve ⚡\n\n&7Tisztességtelen célzás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 20
          multiple: 0.6
          decay: 0.4
        setback:
          enabled: true
          vl-to-setback: 5
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 10
          shuffle-every: 6
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 8
          rotate-every: 5
      g:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 9
        alert-interval: 1
        dont-alert-until: 3
        maximum-ping: 170
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] Aimbot észlelve ⚡\n\n&7Tisztességtelen célzás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 12
          multiple: 0.8
          decay: 0.2
        setback:
          enabled: true
          vl-to-setback: 2
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 4
          shuffle-every: 3
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 3
          rotate-every: 2
      h:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 14
        alert-interval: 1
        dont-alert-until: 4
        maximum-ping: 200
        minimum-tps: 18.5
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] Aimbot észlelve ⚡\n\n&7Tisztességtelen célzás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 16
          multiple: 0.7
          decay: 0.3
        setback:
          enabled: true
          vl-to-setback: 3
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 7
          shuffle-every: 4
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 5
          rotate-every: 3
      i:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 13
        alert-interval: 1
        dont-alert-until: 4
        maximum-ping: 190
        minimum-tps: 18.8
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] Aimbot észlelve ⚡\n\n&7Tisztességtelen célzás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 14
          multiple: 0.75
          decay: 0.25
        setback:
          enabled: true
          vl-to-setback: 3
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 6
          shuffle-every: 4
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 5
          rotate-every: 3
      k:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 22
        alert-interval: 1
        dont-alert-until: 7
        maximum-ping: 280
        minimum-tps: 17.0
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] Aimbot észlelve ⚡\n\n&7Tisztességtelen célzás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 18
          multiple: 0.6
          decay: 0.4
        setback:
          enabled: true
          vl-to-setback: 6
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 12
          shuffle-every: 7
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 10
          rotate-every: 6
      l:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 8
        alert-interval: 1
        dont-alert-until: 2
        maximum-ping: 160
        minimum-tps: 19.2
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] Aimbot észlelve ⚡\n\n&7Tisztességtelen célzás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 12
          multiple: 0.85
          decay: 0.15
        setback:
          enabled: true
          vl-to-setback: 2
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 4
          shuffle-every: 2
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 3
          rotate-every: 2
      m:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 120
        minimum-tps: 19.8
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] Aimbot észlelve ⚡\n\n&7Tisztességtelen célzás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 6
          multiple: 0.95
          decay: 0.05
        setback:
          enabled: true
          vl-to-setback: 1
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 2
          shuffle-every: 1
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 1
          rotate-every: 1
      n:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 16
        alert-interval: 1
        dont-alert-until: 5
        maximum-ping: 210
        minimum-tps: 18.3
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] Aimbot észlelve ⚡\n\n&7Tisztességtelen célzás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 18
          multiple: 0.65
          decay: 0.35
        setback:
          enabled: true
          vl-to-setback: 4
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 8
          shuffle-every: 5
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 6
          rotate-every: 4
      o:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 4
        maximum-ping: 195
        minimum-tps: 18.6
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] Aimbot észlelve ⚡\n\n&7Tisztességtelen célzás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 16
          multiple: 0.7
          decay: 0.3
        setback:
          enabled: true
          vl-to-setback: 3
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 7
          shuffle-every: 4
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 5
          rotate-every: 3
      p:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 7
        alert-interval: 1
        dont-alert-until: 2
        maximum-ping: 140
        minimum-tps: 19.5
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] Aimbot észlelve ⚡\n\n&7Tisztességtelen célzás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 8
          multiple: 0.9
          decay: 0.1
        setback:
          enabled: true
          vl-to-setback: 1
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 3
          shuffle-every: 2
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 2
          rotate-every: 1
      q:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 9
        alert-interval: 1
        dont-alert-until: 3
        maximum-ping: 175
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] Aimbot észlelve ⚡\n\n&7Tisztességtelen célzás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 12
          multiple: 0.8
          decay: 0.2
        setback:
          enabled: true
          vl-to-setback: 2
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 4
          shuffle-every: 3
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 3
          rotate-every: 2
      r:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 12
        alert-interval: 1
        dont-alert-until: 4
        maximum-ping: 300
        minimum-tps: 16.5
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] Aimbot észlelve ⚡\n\n&7Tisztességtelen célzás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 25
          multiple: 0.5
          decay: 0.5
        setback:
          enabled: true
          vl-to-setback: 8
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 8
          shuffle-every: 6
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 6
          rotate-every: 5
      s:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 10
        alert-interval: 1
        dont-alert-until: 3
        maximum-ping: 320
        minimum-tps: 16.0
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] Aimbot észlelve ⚡\n\n&7Tisztességtelen célzás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 28
          multiple: 0.4
          decay: 0.6
        setback:
          enabled: true
          vl-to-setback: 6
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 6
          shuffle-every: 4
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 4
          rotate-every: 3
      u:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 3
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100
        minimum-tps: 19.9
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] Aimbot észlelve ⚡\n\n&7Tisztességtelen célzás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 3
          multiple: 0.98
          decay: 0.02
        setback:
          enabled: true
          vl-to-setback: 1
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 1
          shuffle-every: 1
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 1
          rotate-every: 1
      w:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 11
        alert-interval: 1
        dont-alert-until: 4
        maximum-ping: 350
        minimum-tps: 15.5
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] Aimbot észlelve ⚡\n\n&7Tisztességtelen célzás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 30
          multiple: 0.35
          decay: 0.65
        setback:
          enabled: true
          vl-to-setback: 7
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 7
          shuffle-every: 5
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 5
          rotate-every: 4
      x:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 9
        alert-interval: 1
        dont-alert-until: 3
        maximum-ping: 280
        minimum-tps: 16.8
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] Aimbot észlelve ⚡\n\n&7Tisztességtelen célzás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 18
          multiple: 0.45
          decay: 0.55
        setback:
          enabled: true
          vl-to-setback: 5
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 5
          shuffle-every: 3
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 4
          rotate-every: 3
      y:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 8
        alert-interval: 1
        dont-alert-until: 2
        maximum-ping: 200
        minimum-tps: 18.0
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] Aimbot észlelve ⚡\n\n&7Tisztességtelen célzás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 10
          multiple: 0.8
          decay: 0.2
        setback:
          enabled: true
          vl-to-setback: 3
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 4
          shuffle-every: 2
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 3
          rotate-every: 2
    autoblock:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 6
        alert-interval: 1
        dont-alert-until: 2
        maximum-ping: 150
        minimum-tps: 19.5
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] AutoBlock észlelve ⚡\n\n&7Automatikus blokkolás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 8
          multiple: 0.9
          decay: 0.1
        setback:
          enabled: true
          vl-to-setback: 2
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 3
          shuffle-every: 2
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 2
          rotate-every: 1
      b:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 7
        alert-interval: 1
        dont-alert-until: 3
        maximum-ping: 180
        minimum-tps: 19.2
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] AutoBlock észlelve ⚡\n\n&7Automatikus blokkolás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 10
          multiple: 0.85
          decay: 0.15
        setback:
          enabled: true
          vl-to-setback: 3
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 4
          shuffle-every: 3
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 3
          rotate-every: 2
      c:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 8
        alert-interval: 1
        dont-alert-until: 3
        maximum-ping: 200
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] AutoBlock észlelve ⚡\n\n&7Automatikus blokkolás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 12
          multiple: 0.8
          decay: 0.2
        setback:
          enabled: true
          vl-to-setback: 3
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 4
          shuffle-every: 3
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 3
          rotate-every: 2
      d:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 2
        maximum-ping: 160
        minimum-tps: 19.3
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] AutoBlock észlelve ⚡\n\n&7Automatikus blokkolás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 6
          multiple: 0.9
          decay: 0.1
        setback:
          enabled: true
          vl-to-setback: 2
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 3
          shuffle-every: 2
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 2
          rotate-every: 1
    # AutoClicker checks optimalizálva a pontosságra és gyorsaságra
    # Magasabb violation limitekkel a false flag-ek elkerülése érdekében
    autoclicker:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 12
        alert-interval: 1
        dont-alert-until: 4
        maximum-ping: 120
        minimum-tps: 19.8
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] AutoClicker észlelve ⚡\n\n&7Automatikus kattintás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        max-cps: 18
        buffer:
          max: 15
          multiple: 0.9
          decay: 0.1
        setback:
          enabled: true
          vl-to-setback: 4
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 6
          shuffle-every: 4
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 5
          rotate-every: 3
      b:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 20
        alert-interval: 1
        dont-alert-until: 8
        maximum-ping: 200
        minimum-tps: 18.5
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] AutoClicker észlelve ⚡\n\n&7Automatikus kattintás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 25
          multiple: 0.7
          decay: 0.3
        setback:
          enabled: true
          vl-to-setback: 10
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 12
          shuffle-every: 8
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 10
          rotate-every: 6
      c:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 18
        alert-interval: 1
        dont-alert-until: 6
        maximum-ping: 180
        minimum-tps: 19.2
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] AutoClicker észlelve ⚡\n\n&7Automatikus kattintás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 20
          multiple: 0.75
          decay: 0.25
        setback:
          enabled: true
          vl-to-setback: 8
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 10
          shuffle-every: 6
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 8
          rotate-every: 5
      d:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 16
        alert-interval: 1
        dont-alert-until: 5
        maximum-ping: 160
        minimum-tps: 19.4
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] AutoClicker észlelve ⚡\n\n&7Automatikus kattintás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 18
          multiple: 0.8
          decay: 0.2
        setback:
          enabled: true
          vl-to-setback: 6
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 8
          shuffle-every: 5
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 6
          rotate-every: 4
      e:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 22
        alert-interval: 1
        dont-alert-until: 8
        maximum-ping: 220
        minimum-tps: 18.8
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] AutoClicker észlelve ⚡\n\n&7Automatikus kattintás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 25
          multiple: 0.7
          decay: 0.3
        setback:
          enabled: true
          vl-to-setback: 12
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 14
          shuffle-every: 8
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 12
          rotate-every: 7
      f:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 5
        maximum-ping: 140
        minimum-tps: 19.6
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] AutoClicker észlelve ⚡\n\n&7Automatikus kattintás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 16
          multiple: 0.85
          decay: 0.15
        setback:
          enabled: true
          vl-to-setback: 5
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 7
          shuffle-every: 4
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 5
          rotate-every: 3
      g:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 25
        alert-interval: 1
        dont-alert-until: 10
        maximum-ping: 300
        minimum-tps: 17.5
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] AutoClicker észlelve ⚡\n\n&7Automatikus kattintás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 35
          multiple: 0.5
          decay: 0.5
        setback:
          enabled: true
          vl-to-setback: 15
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 18
          shuffle-every: 12
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 15
          rotate-every: 10
      h:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 20
        alert-interval: 1
        dont-alert-until: 7
        maximum-ping: 200
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] AutoClicker észlelve ⚡\n\n&7Automatikus kattintás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 22
          multiple: 0.75
          decay: 0.25
        setback:
          enabled: true
          vl-to-setback: 9
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 12
          shuffle-every: 7
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 10
          rotate-every: 6
      i:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 28
        alert-interval: 1
        dont-alert-until: 12
        maximum-ping: 350
        minimum-tps: 17.0
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] AutoClicker észlelve ⚡\n\n&7Automatikus kattintás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 40
          multiple: 0.4
          decay: 0.6
        setback:
          enabled: true
          vl-to-setback: 18
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 20
          shuffle-every: 14
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 18
          rotate-every: 12
      j:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 24
        alert-interval: 1
        dont-alert-until: 9
        maximum-ping: 250
        minimum-tps: 18.5
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] AutoClicker észlelve ⚡\n\n&7Automatikus kattintás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 28
          multiple: 0.65
          decay: 0.35
        setback:
          enabled: true
          vl-to-setback: 12
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 15
          shuffle-every: 9
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 12
          rotate-every: 8
      k:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 21
        alert-interval: 1
        dont-alert-until: 8
        maximum-ping: 230
        minimum-tps: 18.8
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] AutoClicker észlelve ⚡\n\n&7Automatikus kattintás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 24
          multiple: 0.7
          decay: 0.3
        setback:
          enabled: true
          vl-to-setback: 10
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 13
          shuffle-every: 8
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 10
          rotate-every: 7
      l:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 19
        alert-interval: 1
        dont-alert-until: 6
        maximum-ping: 190
        minimum-tps: 19.1
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] AutoClicker észlelve ⚡\n\n&7Automatikus kattintás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 20
          multiple: 0.8
          decay: 0.2
        setback:
          enabled: true
          vl-to-setback: 8
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 10
          shuffle-every: 6
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 8
          rotate-every: 5
      m:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 17
        alert-interval: 1
        dont-alert-until: 5
        maximum-ping: 170
        minimum-tps: 19.3
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] AutoClicker észlelve ⚡\n\n&7Automatikus kattintás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 18
          multiple: 0.85
          decay: 0.15
        setback:
          enabled: true
          vl-to-setback: 6
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 8
          shuffle-every: 5
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 6
          rotate-every: 4
      n:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 35
        alert-interval: 1
        dont-alert-until: 15
        maximum-ping: 400
        minimum-tps: 16.5
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] AutoClicker észlelve ⚡\n\n&7Automatikus kattintás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 50
          multiple: 0.3
          decay: 0.7
        setback:
          enabled: true
          vl-to-setback: 25
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 25
          shuffle-every: 18
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 20
          rotate-every: 15
      o:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 26
        alert-interval: 1
        dont-alert-until: 10
        maximum-ping: 280
        minimum-tps: 18.2
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] AutoClicker észlelve ⚡\n\n&7Automatikus kattintás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 30
          multiple: 0.6
          decay: 0.4
        setback:
          enabled: true
          vl-to-setback: 14
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 16
          shuffle-every: 10
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 14
          rotate-every: 9
      p:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 14
        alert-interval: 1
        dont-alert-until: 4
        maximum-ping: 130
        minimum-tps: 19.7
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] AutoClicker észlelve ⚡\n\n&7Automatikus kattintás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 12
          multiple: 0.9
          decay: 0.1
        setback:
          enabled: true
          vl-to-setback: 4
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 6
          shuffle-every: 3
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 4
          rotate-every: 2
      q:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 40
        alert-interval: 1
        dont-alert-until: 18
        maximum-ping: 450
        minimum-tps: 16.0
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] AutoClicker észlelve ⚡\n\n&7Automatikus kattintás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 60
          multiple: 0.25
          decay: 0.75
        setback:
          enabled: true
          vl-to-setback: 30
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 30
          shuffle-every: 20
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 25
          rotate-every: 18
      r:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 45
        alert-interval: 1
        dont-alert-until: 20
        maximum-ping: 500
        minimum-tps: 15.5
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] AutoClicker észlelve ⚡\n\n&7Automatikus kattintás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 80
          multiple: 0.2
          decay: 0.8
        setback:
          enabled: true
          vl-to-setback: 35
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 35
          shuffle-every: 25
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 30
          rotate-every: 22
      s:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 32
        alert-interval: 1
        dont-alert-until: 14
        maximum-ping: 320
        minimum-tps: 17.8
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] AutoClicker észlelve ⚡\n\n&7Automatikus kattintás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 45
          multiple: 0.4
          decay: 0.6
        setback:
          enabled: true
          vl-to-setback: 20
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 22
          shuffle-every: 15
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 18
          rotate-every: 13
      t:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 30
        alert-interval: 1
        dont-alert-until: 12
        maximum-ping: 300
        minimum-tps: 18.0
        punishment-commands:
        - kick %player% &c&l⚡ [VULCAN] AutoClicker észlelve ⚡\n\n&7Automatikus kattintás miatt ki lettél rúgva!\n&7Fellebbezés: discord.gg/server
        buffer:
          max: 35
          multiple: 0.5
          decay: 0.5
        setback:
          enabled: true
          vl-to-setback: 18
        hotbar-shuffle:
          enabled: true
          minimum-vl-to-shuffle: 20
          shuffle-every: 12
        random-rotation:
          enabled: true
          minimum-vl-to-randomly-rotate: 16
          rotate-every: 10
        min-kurtosis: 13
    reach:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 8
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.25
          decay: 0.035
        max-reach: 3.03
      b:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 6
          multiple: 0.25
          decay: 0.225
        max-reach: 3.15
    fastbow:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.5
          decay: 0.125
        hotbar-shuffle:
          enabled: false
          minimum-vl-to-shuffle: 2
          shuffle-every: 2
        random-rotation:
          enabled: false
          minimum-vl-to-randomly-rotate: 2
          rotate-every: 2
    criticals:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 10
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 1
          multiple: 0.5
          decay: 0.05
        hotbar-shuffle:
          enabled: false
          minimum-vl-to-shuffle: 2
          shuffle-every: 2
        random-rotation:
          enabled: false
          minimum-vl-to-randomly-rotate: 2
          rotate-every: 2
      b:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 10
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 1
          multiple: 0.5
          decay: 0.05
        hotbar-shuffle:
          enabled: false
          minimum-vl-to-shuffle: 2
          shuffle-every: 2
        random-rotation:
          enabled: false
          minimum-vl-to-randomly-rotate: 2
          rotate-every: 2
    killaura:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 3
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.5
          decay: 0.5
        hotbar-shuffle:
          enabled: false
          minimum-vl-to-shuffle: 2
          shuffle-every: 2
        random-rotation:
          enabled: false
          minimum-vl-to-randomly-rotate: 2
          rotate-every: 2
      b:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 3
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 5
          multiple: 0.6
          decay: 0.5
        hotbar-shuffle:
          enabled: false
          minimum-vl-to-shuffle: 2
          shuffle-every: 2
        random-rotation:
          enabled: false
          minimum-vl-to-randomly-rotate: 2
          rotate-every: 2
      c:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 2
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 2
          multiple: 0.5
          decay: 0.25
        hotbar-shuffle:
          enabled: false
          minimum-vl-to-shuffle: 2
          shuffle-every: 2
        random-rotation:
          enabled: false
          minimum-vl-to-randomly-rotate: 2
          rotate-every: 2
      d:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 1
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
      f:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 3
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 10
          multiple: 0.25
          decay: 2
      h:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 1
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
      j:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 1
          multiple: 0.5
          decay: 0.5
      k:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.5
          decay: 0.75
      l:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 5
          multiple: 0.6
          decay: 0.5
        hotbar-shuffle:
          enabled: false
          minimum-vl-to-shuffle: 2
          shuffle-every: 2
        random-rotation:
          enabled: false
          minimum-vl-to-randomly-rotate: 2
          rotate-every: 2
    hitbox:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 10
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 4
          multiple: 0.25
          decay: 0.125
        max-angle: 0.36
      b:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 6
          multiple: 0.25
          decay: 0.125
        max-angle: 45
    velocity:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 2
          multiple: 0.25
          decay: 0.075
      b:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 4
          multiple: 0.35
          decay: 0.125
      c:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 5
          multiple: 0.25
          decay: 0.175
      d:
        enabled: false
        punishable: false
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 4
          multiple: 0.25
          decay: 0.05
  movement:
    noslow:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.25
          decay: 0.15
      b:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 7
          multiple: 0.25
          decay: 0.075
      c:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 7
          multiple: 0.25
          decay: 0.15
      d:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 7
          multiple: 0.25
          decay: 0.15
      e:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 7
          multiple: 0.25
          decay: 0.15
    motion:
      a:
        enabled: true
        punishable: true
        setback: false
        min-vl-to-setback: 1
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.25
          decay: 0.25
      b:
        enabled: true
        punishable: true
        setback: false
        min-vl-to-setback: 1
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.25
          decay: 0.25
      c:
        enabled: true
        punishable: true
        setback: false
        min-vl-to-setback: 1
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 1
          multiple: 0.5
          decay: 0.05
      d:
        enabled: true
        punishable: true
        setback: false
        min-vl-to-setback: 1
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.25
          decay: 0.05
      e:
        enabled: true
        punishable: true
        setback: false
        min-vl-to-setback: 1
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 3
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 1
          multiple: 0.5
          decay: 0.05
      g:
        enabled: true
        punishable: true
        setback: false
        min-vl-to-setback: 1
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 20
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 2
          multiple: 0.25
          decay: 0.15
      h:
        enabled: false
        punishable: true
        setback: false
        min-vl-to-setback: 1
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 20
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 4
          multiple: 0.25
          decay: 0.15
    jump:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        setback: false
        min-vl-to-setback: 1
        broadcast-punishment: false
        max-violations: 25
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 2
          multiple: 0.25
          decay: 0.15
      b:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        setback: false
        min-vl-to-setback: 1
        broadcast-punishment: false
        max-violations: 25
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 2
          multiple: 0.25
          decay: 0.15
      c:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        setback: false
        min-vl-to-setback: 1
        broadcast-punishment: false
        max-violations: 10
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 2
          multiple: 0.25
          decay: 0.15
    step:
      a:
        enabled: true
        punishable: true
        setback: false
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
      b:
        enabled: true
        punishable: true
        setback: false
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 6
          multiple: 0.25
          decay: 0.35
      c:
        enabled: true
        punishable: true
        setback: false
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 4
          multiple: 0.25
          decay: 0.1
    boatfly:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 4
          multiple: 0.33
          decay: 0.125
        kick-out: false
      b:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 5
          multiple: 0.25
          decay: 0.25
        kick-out: false
      c:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 5
          multiple: 0.25
          decay: 0.125
        kick-out: false
    entityflight:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 5
          multiple: 0.5
          decay: 0.25
        kick-out: false
      b:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 5
          multiple: 0.5
          decay: 0.25
    antilevitation:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 8
          multiple: 0.25
          decay: 0.075
    nosaddle:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 6
          multiple: 0.5
          decay: 0.125
    entityspeed:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 5
          multiple: 0.5
          decay: 0.25
        kick-out: false
        min-difference: 0.01
    elytra:
      a:
        enabled: true
        punishable: true
        setback: false
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 1
          multiple: 0.5
          decay: 0.25
        max-speed: 4.25
      b:
        enabled: true
        punishable: true
        setback: false
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 8
          multiple: 0.25
          decay: 0.125
      c:
        enabled: true
        punishable: true
        setback: false
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 6
          multiple: 0.5
          decay: 0.125
      f:
        enabled: true
        punishable: true
        setback: false
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 10
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 6
          multiple: 0.25
          decay: 0.125
      g:
        enabled: true
        punishable: false
        setback: false
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 25
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.25
          decay: 0.1
      i:
        enabled: true
        punishable: true
        setback: false
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 4
          multiple: 0.25
          decay: 0.1
      k:
        enabled: true
        punishable: true
        setback: false
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 4
          multiple: 0.25
          decay: 0.1
      l:
        enabled: true
        punishable: true
        setback: false
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 4
          multiple: 0.25
          decay: 0.1
      m:
        enabled: true
        punishable: true
        setback: false
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 4
          multiple: 0.25
          decay: 0.1
      n:
        enabled: false
        punishable: false
        discord-alert-interval: 1
        setback: false
        min-vl-to-setback: 1
        broadcast-punishment: false
        max-violations: 20
        alert-interval: 1
        dont-alert-until: 2
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.25
          decay: 0.045
    speed:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        setback: false
        min-vl-to-setback: 1
        broadcast-punishment: false
        max-violations: 12
        alert-interval: 1
        dont-alert-until: 2
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 5
          multiple: 0.35
          decay: 0.125
      b:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        setback: false
        min-vl-to-setback: 1
        broadcast-punishment: false
        max-violations: 20
        alert-interval: 1
        dont-alert-until: 2
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.25
          decay: 0.045
        min-difference: 0.0025
      c:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        setback: false
        min-vl-to-setback: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 2
        dont-alert-until: 2
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.25
          decay: 0.045
        min-difference: 0.001
      d:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        setback: false
        min-vl-to-setback: 1
        broadcast-punishment: false
        max-violations: 20
        alert-interval: 1
        dont-alert-until: 2
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.25
          decay: 0.045
        min-difference: 0.0025
      e:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        setback: false
        min-vl-to-setback: 1
        broadcast-punishment: false
        max-violations: 20
        alert-interval: 1
        dont-alert-until: 2
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 2
          multiple: 0.25
          decay: 0.025
    fastclimb:
      a:
        enabled: true
        punishable: true
        setback: false
        min-vl-to-setback: 1
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 4
          multiple: 0.5
          decay: 0.125
    wallclimb:
      a:
        enabled: true
        punishable: true
        setback: false
        min-vl-to-setback: 1
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 4
          multiple: 0.35
          decay: 0.225
    jesus:
      a:
        enabled: true
        punishable: true
        setback: false
        min-vl-to-setback: 1
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 2
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.5
          decay: 0.025
      b:
        enabled: true
        punishable: true
        setback: false
        min-vl-to-setback: 1
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 2
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 4
          multiple: 0.5
          decay: 0.175
      c:
        enabled: true
        punishable: true
        setback: false
        min-vl-to-setback: 1
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 2
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 10
          multiple: 0.45
          decay: 0.75
      d:
        enabled: true
        punishable: true
        setback: false
        min-vl-to-setback: 1
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 4
          multiple: 0.45
          decay: 0.175
      e:
        enabled: true
        punishable: true
        setback: false
        min-vl-to-setback: 1
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 2
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 10
          multiple: 0.25
          decay: 0.175
    flight:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 5
        setback: false
        min-vl-to-setback: 1
        broadcast-punishment: false
        max-violations: 30
        alert-interval: 2
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.65
          decay: 0.275
        ignore-ghost-blocks: true
      b:
        enabled: true
        punishable: true
        discord-alert-interval: 5
        setback: false
        min-vl-to-setback: 1
        broadcast-punishment: false
        max-violations: 30
        alert-interval: 2
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.5
          decay: 0.275
      c:
        enabled: true
        punishable: true
        discord-alert-interval: 5
        setback: false
        min-vl-to-setback: 1
        broadcast-punishment: false
        max-violations: 30
        alert-interval: 2
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 4
          multiple: 0.75
          decay: 0.225
        ignore-ghost-blocks: true
      d:
        enabled: true
        punishable: true
        discord-alert-interval: 5
        setback: false
        min-vl-to-setback: 1
        broadcast-punishment: false
        max-violations: 30
        alert-interval: 2
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 4
          multiple: 0.35
          decay: 0.275
        ignore-ghost-blocks: true
      e:
        enabled: true
        punishable: true
        discord-alert-interval: 5
        setback: false
        min-vl-to-setback: 1
        broadcast-punishment: false
        max-violations: 30
        alert-interval: 2
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.4
          decay: 0.5
        ignore-ghost-blocks: true
      f:
        enabled: true
        punishable: true
        discord-alert-interval: 5
        setback: false
        min-vl-to-setback: 1
        broadcast-punishment: false
        max-violations: 30
        alert-interval: 2
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 2
          multiple: 0.4
          decay: 0.04
    vclip:
      a:
        enabled: true
        punishable: false
        setback: false
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 20
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
    sprint:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 6
          multiple: 0.25
          decay: 0.4
    strafe:
      a:
        enabled: true
        punishable: true
        setback: false
        min-vl-to-setback: 1
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 2
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 2
          multiple: 0.5
          decay: 0.05
      b:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 4
          multiple: 0.15
          decay: 0.075
  player:
    fastuse:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
    fastplace:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        max-blocks: 15
      b:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 1
          multiple: 0.25
          decay: 0.5
        max-blocks: 4
    fastbreak:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 2
          multiple: 0.25
          decay: 0.5
        min-difference: -125
    tower:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 4
          multiple: 0.5
          decay: 0.75
    scaffold:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 3
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
      b:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 10
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.25
          decay: 0.25
      c:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 2
          multiple: 0.25
          decay: 0.5
      d:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 10
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 4
          multiple: 0.25
          decay: 0.5
      e:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 10
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 4
          multiple: 0.25
          decay: 0.5
      f:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 3
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.5
          decay: 0.1
      g:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 2
          multiple: 0.25
          decay: 0.25
      h:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 8
          multiple: 0.25
          decay: 0.35
      i:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 10
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
      j:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
      k:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 1
          multiple: 0.5
          decay: 0.35
        max-blocks: 6
      m:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 12
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 1
          multiple: 0.5
          decay: 0.25
      n:
        enabled: false
        punishable: false
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 12
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.5
          decay: 0.25
      o:
        enabled: false
        punishable: false
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 12
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
    timer:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 2
        dont-alert-until: 3
        maximum-ping: 100000
        minimum-tps: 19.8
        punishment-commands:
        - 'minecraft:kick %player% Internal Exception: io.netty.handler.timeout.ReadTimeoutException'
        buffer:
          max: 35
          multiple: 0.425
          decay: 1.85
        max-speed: 1.05
      d:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 35
        alert-interval: 2
        dont-alert-until: 8
        maximum-ping: 100000
        minimum-tps: 19.8
        punishment-commands:
        - 'minecraft:kick %player% Internal Exception: io.netty.handler.timeout.ReadTimeoutException'
        buffer:
          max: 3
          multiple: 0.5
          decay: 0.25
        max-balance: 75
    baritone:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 20
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 20
          multiple: 0.5
          decay: 0.25
      b:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 20
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 2
          multiple: 0.5
          decay: 0.05
    ghosthand:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 3
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        cancel: true
    airplace:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 10
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
    badpackets:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 3
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 2
          multiple: 0.5
          decay: 0.25
      b:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 1
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
      c:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 1
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
      d:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.25
          decay: 0.01
      e:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 1
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
      f:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 1
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
      g:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 3
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
      h:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 3
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
      i:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 3
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
      j:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 3
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
      k:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.5
          decay: 0.4
      m:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 5
          multiple: 0.33
          decay: 0.25
      n:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 10
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 20
          multiple: 0.25
          decay: 2.5
      o:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 1
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.5
          decay: 0.15
      p:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 10
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.5
          decay: 0.15
      q:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 1
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.5
          decay: 0.15
      r:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 10
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 6
          multiple: 0.5
          decay: 0.25
      s:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
      t:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 1
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
      u:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 1
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
      v:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 8
          multiple: 0.5
          decay: 0.075
        kick-out: false
      w:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 10
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 8
          multiple: 0.5
          decay: 0.75
      x:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 10
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 6
          multiple: 0.5
          decay: 0.35
      y:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 1
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
      z:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 3
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
      '1':
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 12
          multiple: 0.25
          decay: 0.25
      '2':
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 12
          multiple: 0.25
          decay: 0.25
      '4':
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 4
          multiple: 0.25
          decay: 0.025
      '5':
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
      '6':
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.25
          decay: 0.05
      '7':
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 10
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 10
          multiple: 0.25
          decay: 0.05
      '8':
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 10
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 5
          multiple: 0.25
          decay: 0.05
    inventory:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 2
          multiple: 0.25
          decay: 0.25
      b:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 2
          multiple: 0.25
          decay: 0.25
    invalid:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        setback: false
        min-vl-to-setback: 1
        broadcast-punishment: false
        max-violations: 8
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 2
          multiple: 0.25
          decay: 0.05
      c:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        setback: false
        min-vl-to-setback: 1
        broadcast-punishment: false
        max-violations: 8
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.25
          decay: 0.045
      d:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 10
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 8
          multiple: 0.25
          decay: 0.5
      e:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        setback: false
        min-vl-to-setback: 1
        broadcast-punishment: false
        max-violations: 8
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 1
          multiple: 0.5
          decay: 0.01
      f:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        setback: false
        min-vl-to-setback: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 1
          multiple: 0.5
          decay: 0.01
      g:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        setback: false
        min-vl-to-setback: 1
        broadcast-punishment: false
        max-violations: 20
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 2
          multiple: 0.5
          decay: 0.025
      h:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        setback: false
        min-vl-to-setback: 1
        broadcast-punishment: false
        max-violations: 20
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.5
          decay: 0.05
      i:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        setback: false
        min-vl-to-setback: 1
        broadcast-punishment: false
        max-violations: 20
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 3
          multiple: 0.5
          decay: 0.05
      j:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        setback: false
        min-vl-to-setback: 1
        broadcast-punishment: false
        max-violations: 20
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 1
          multiple: 0.5
          decay: 0.05
    groundspoof:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 4
          multiple: 0.5
          decay: 0.125
      b:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 5
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 4
          multiple: 0.5
          decay: 0.125
      c:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 15
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        buffer:
          max: 4
          multiple: 0.5
          decay: 0.125
    improbable:
      a:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 1
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        max-combat-violations: 25
      b:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 1
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        max-movement-violations: 30
      c:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 1
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        max-player-violations: 30
      d:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 1
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        max-autoclicker-violations: 30
      e:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 1
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        max-total-violations: 50
      f:
        enabled: true
        punishable: true
        discord-alert-interval: 1
        broadcast-punishment: false
        max-violations: 1
        alert-interval: 1
        dont-alert-until: 1
        maximum-ping: 100000
        minimum-tps: 19.0
        punishment-commands:
        - kick %player% &c[Vulcan] Unfair Advantage
        max-scaffold-violations: 25